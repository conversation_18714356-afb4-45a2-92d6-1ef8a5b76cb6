#include "../include/detection_processor.h"
#include "../include/data_type.h"
#include "../include/app_config.h"
#include "../include/logger.h"
#include <iostream>
#include <iomanip>
#include <string>
#include <vector>

void printUsage(const char* program_name) {
    std::cout << "用法: " << program_name << " [选项]" << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  --debug     启用调试模式，显示详细日志信息" << std::endl;
    std::cout << "  --info      设置日志级别为INFO（默认）" << std::endl;
    std::cout << "  --warning   设置日志级别为WARNING" << std::endl;
    std::cout << "  --error     设置日志级别为ERROR" << std::endl;
    std::cout << "  --help      显示此帮助信息" << std::endl;
}

int main(int argc, char* argv[]) {
    try {
        // 默认日志级别为INFO
        Logger::Level log_level = Logger::INFO;

        // 解析命令行参数
        for (int i = 1; i < argc; ++i) {
            std::string arg = argv[i];
            if (arg == "--debug") {
                log_level = Logger::DEBUG;
                std::cout << "启用调试模式" << std::endl;
            } else if (arg == "--info") {
                log_level = Logger::INFO;
                std::cout << "设置日志级别为INFO" << std::endl;
            } else if (arg == "--warning") {
                log_level = Logger::WARNING;
                std::cout << "设置日志级别为WARNING" << std::endl;
            } else if (arg == "--error") {
                log_level = Logger::ERROR;
                std::cout << "设置日志级别为ERROR" << std::endl;
            } else if (arg == "--help" || arg == "-h") {
                printUsage(argv[0]);
                return 0;
            } else {
                std::cerr << "未知参数: " << arg << std::endl;
                printUsage(argv[0]);
                return 1;
            }
        }

        // 设置日志级别
        Logger::setLevel(log_level);

        std::cout << "=== 像素到物理距离转换测试程序 ===" << std::endl;
        std::cout << std::endl;

        // 加载应用配置
        AppConfig config;

        // 尝试从JSON文件加载配置
        std::string json_config_path = "../config/app_config.json";
        if (config.loadFromJson(json_config_path)) {
            std::cout << "成功从JSON文件加载配置: " << json_config_path << std::endl;
        } else {
            std::cerr << "错误: 无法加载JSON配置文件: " << json_config_path << std::endl;
            std::cerr << "请确保配置文件存在且格式正确" << std::endl;
            return 1;
        }
        std::cout << std::endl;

        // 创建检测处理器实例
        DetectionProcessor processor;
        auto config_ptr = std::make_shared<AppConfig>(config);
        processor.initializeDistanceTable(config.distance_table_path, config_ptr);

        // 尝试加载JSON配置文件
        processor.setSizeRangesConfigFromJson(config.size_ranges_config);

        // 加载相机内参
        if (processor.initializeCameraIntrinsics(config.calib_intrix_path)) {
            std::cout << "成功加载相机内参: " << config.calib_intrix_path << std::endl;
        } else {
            std::cerr << "警告: 无法加载相机内参文件: " << config.calib_intrix_path << std::endl;
            std::cerr << "相机坐标转换功能将不可用" << std::endl;
        }

        // 测试1: 单个目标测试
        std::cout << "=== 测试1: 单个目标测试 ===" << std::endl;

        BBox bbox1;
        bbox1.xmin = 56; bbox1.ymin = 400; bbox1.xmax = 788; bbox1.ymax = 1125;
        bbox1.label = 5; bbox1.score = 0.9f;
        float measure_flag1 = 1.0f;  // 测距标志位，1表示需要测距

        BBoxFlag bbox_flag1;
        bbox_flag1.box = bbox1;
        bbox_flag1.flag = measure_flag1;
        DetectionResult det1(bbox_flag1);

        std::cout << "输入检测框: (" << det1.bbox_flag.box.xmin << ", " << det1.bbox_flag.box.ymin << ", "
                  << det1.bbox_flag.box.xmax << ", " << det1.bbox_flag.box.ymax << ")" << std::endl;
        std::cout << "测距标志位: " << det1.bbox_flag.flag << std::endl;

        // 处理检测结果
        auto result1 = processor.processDetectionResult(det1);

        std::cout << "处理结果:" << std::endl;
        std::cout << "  物理距离: " << std::fixed << std::setprecision(2) << result1.physical_distance << " cm" << std::endl;
        std::cout << "  左下角距离: " << result1.left_distance << " cm" << std::endl;
        std::cout << "  右下角距离: " << result1.right_distance << " cm" << std::endl;
        std::cout << "  目标长度: " << result1.length << " cm" << std::endl;

        // 输出相机坐标系转换结果
        std::cout << "=== 相机坐标系转换结果 ===" << std::endl;
        if (result1.camera_coords.bottom_right.z > 0) {  // 检查是否有有效的相机坐标
            std::cout << "  左下角: ("
                      << result1.camera_coords.bottom_left.x << ", "
                      << result1.camera_coords.bottom_left.y << ", "
                      << result1.camera_coords.bottom_left.z << ") cm" << std::endl;
            std::cout << "  右下角: ("
                      << result1.camera_coords.bottom_right.x << ", "
                      << result1.camera_coords.bottom_right.y << ", "
                      << result1.camera_coords.bottom_right.z << ") cm" << std::endl;
        }
        std::cout << std::endl;

    //     // 测试2: 多个目标测试 - 这里可能会导致崩溃
    //     std::cout << "=== 测试2: 多个目标测试 ===" << std::endl;

    //     // 创建多个检测结果
    //     std::vector<DetectionResult> detections;

    //     // 目标1
    //     BBox bbox2;
    //     bbox2.xmin = 100; bbox2.ymin = 300; bbox2.xmax = 200; bbox2.ymax = 400;
    //     bbox2.label = 2; bbox2.score = 0.85f;
    //     detections.emplace_back(BBoxFlag(bbox2, 1.0f));

    //     // 目标2
    //     BBox bbox3;
    //     bbox3.xmin = 300; bbox3.ymin = 350; bbox3.xmax = 450; bbox3.ymax = 450;
    //     bbox3.label = 3; bbox3.score = 0.92f;
    //     detections.emplace_back(BBoxFlag(bbox3, 1.0f));

    //     // 目标3
    //     BBox bbox4;
    //     bbox4.xmin = 500; bbox4.ymin = 280; bbox4.xmax = 650; bbox4.ymax = 380;
    //     bbox4.label = 1; bbox4.score = 0.78f;
    //     detections.emplace_back(BBoxFlag(bbox4, 1.0f));

    //     std::cout << "开始处理 " << detections.size() << " 个目标..." << std::endl;

    //     // 处理多个目标
    //     for (size_t i = 0; i < detections.size(); ++i) {
    //         try {
    //             std::cout << "\n--- 处理目标 " << (i + 1) << " ---" << std::endl;
    //             std::cout << "检测框: (" << detections[i].bbox_flag.box.xmin << ", "
    //                       << detections[i].bbox_flag.box.ymin << ", "
    //                       << detections[i].bbox_flag.box.xmax << ", "
    //                       << detections[i].bbox_flag.box.ymax << ")" << std::endl;
    //             std::cout << "标签: " << detections[i].bbox_flag.box.label
    //                       << ", 置信度: " << detections[i].bbox_flag.box.score << std::endl;

    //             // 这里可能会崩溃
    //             auto result = processor.processDetectionResult(detections[i]);

    //             std::cout << "处理结果:" << std::endl;
    //             std::cout << "  物理距离: " << std::fixed << std::setprecision(2)
    //                       << result.physical_distance << " cm" << std::endl;
    //             std::cout << "  左下角距离: " << result.left_distance << " cm" << std::endl;
    //             std::cout << "  右下角距离: " << result.right_distance << " cm" << std::endl;
    //             std::cout << "  目标长度: " << result.length << " cm" << std::endl;

    //             // 输出相机坐标系转换结果
    //             if (result.camera_coords.bottom_right.z > 0) {
    //                 std::cout << "  相机坐标 - 左下角: ("
    //                           << result.camera_coords.bottom_left.x << ", "
    //                           << result.camera_coords.bottom_left.y << ", "
    //                           << result.camera_coords.bottom_left.z << ") cm" << std::endl;
    //                 std::cout << "  相机坐标 - 右下角: ("
    //                           << result.camera_coords.bottom_right.x << ", "
    //                           << result.camera_coords.bottom_right.y << ", "
    //                           << result.camera_coords.bottom_right.z << ") cm" << std::endl;
    //             }

    //         } catch (const std::exception& e) {
    //             std::cerr << "处理目标 " << (i + 1) << " 时发生错误: " << e.what() << std::endl;
    //             // 继续处理下一个目标，不要因为一个目标失败就退出
    //             continue;
    //         }
    //     }

    //     std::cout << "\n多目标处理完成!" << std::endl;

    //     // 测试2: 多个目标测试 - 检查内存泄漏
    //     std::cout << "\n=== 测试2: 多个目标内存测试 ===" << std::endl;

    //     // 创建多个检测结果进行循环测试
    //     for (int loop = 0; loop < 10; ++loop) {
    //         std::cout << "\n--- 循环 " << (loop + 1) << " ---" << std::endl;

    //         std::vector<DetectionResult> detections;

    //         // 创建多个目标
    //         for (int i = 0; i < 5; ++i) {
    //             BBox bbox;
    //             bbox.xmin = 100 + i * 50;
    //             bbox.ymin = 300 + i * 20;
    //             bbox.xmax = bbox.xmin + 100;
    //             bbox.ymax = bbox.ymin + 80;
    //             bbox.label = i % 6;
    //             bbox.score = 0.8f + i * 0.02f;

    //             BBoxFlag bbox_flag;
    //             bbox_flag.box = bbox;
    //             bbox_flag.flag = 1.0f;  // 需要测距

    //             detections.emplace_back(bbox_flag);
    //         }

    //         std::cout << "处理 " << detections.size() << " 个目标..." << std::endl;

    //         // 处理每个目标
    //         for (size_t i = 0; i < detections.size(); ++i) {
    //             try {
    //                 auto result = processor.processDetectionResult(detections[i]);

    //                 // 简化输出，只显示关键信息
    //                 if (i == 0) {  // 只显示第一个目标的详细信息
    //                     std::cout << "目标 " << (i + 1) << ": 距离="
    //                               << std::fixed << std::setprecision(1)
    //                               << result.physical_distance << "cm, 长度="
    //                               << result.length << "cm" << std::endl;
    //                 }

    //             } catch (const std::exception& e) {
    //                 std::cerr << "处理目标 " << (i + 1) << " 时发生错误: " << e.what() << std::endl;
    //             }
    //         }

    //         // 清理检测结果向量
    //         detections.clear();

    //         // 每隔几次循环显示内存使用提示
    //         if ((loop + 1) % 3 == 0) {
    //             std::cout << "已完成 " << (loop + 1) << " 次循环测试" << std::endl;
    //         }
    //     }

    //     std::cout << "\n多目标内存测试完成!" << std::endl;
    //     std::cout << "如果程序正常结束，说明没有明显的内存泄漏问题" << std::endl;

    //     return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}

